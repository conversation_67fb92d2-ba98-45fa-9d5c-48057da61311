{"id": "api_management", "title": "全部API", "components": [{"id": "layout", "type": "TopDownLayout", "props": {"headerHeight": 64, "footerHeight": 32, "mode": "modern"}, "children": [{"id": "top_nav", "type": "TopNavigation", "props": {"logo": {"src": "data:image/png;base64,<LOGO_DATA_TO_BE_FILLED>", "href": "/"}, "menu": [{"key": "overview", "label": "概览", "href": "/overview"}, {"key": "apis", "label": "APIs", "href": "/apis", "children": [{"key": "api", "label": "API", "href": "/apis/api"}, {"key": "app", "label": "应用", "href": "/apis/app"}]}, {"key": "data", "label": "数据", "href": "/data"}, {"key": "weekness", "label": "弱点", "href": "/weekness"}, {"key": "risk", "label": "风险", "href": "/risk"}, {"key": "audit", "label": "审计", "href": "/audit"}, {"key": "report", "label": "报告", "href": "/report"}, {"key": "trend", "label": "态势", "href": "/trend"}, {"key": "config", "label": "配置", "href": "/config"}], "user": {"name": "book", "menu": [{"key": "logout", "label": "退出"}]}}}, {"id": "table_with_view", "type": "TableWithView", "props": {"viewConfig": {"width": 240, "searchPlaceholder": "搜索视图", "showSearch": true}, "title": "全部API", "columns": [{"key": "path", "title": "路径", "dataIndex": "path", "width": "33%"}, {"key": "sensitivity", "title": "API敏感等级", "dataIndex": "sensitivity", "width": "16%"}, {"key": "riskLevel", "title": "API风险等级", "dataIndex": "riskLevel", "width": "16%"}, {"key": "totalVisits", "title": "累计访问次数", "dataIndex": "totalVisits", "width": "16%", "sortable": true}, {"key": "trafficSource", "title": "流量来源", "dataIndex": "trafficSource", "width": "16%"}, {"key": "firstSeen", "title": "首次发现时间", "dataIndex": "firstSeen", "width": "25%", "sortable": true}], "searchFields": [{"key": "application", "label": "应用", "type": "input", "placeholder": "请输入"}, {"key": "sensitivityLevel", "label": "API敏感等级", "type": "select", "placeholder": "请选择", "options": [{"label": "高敏感", "value": "high"}, {"label": "中敏感", "value": "medium"}, {"label": "低敏感", "value": "low"}]}, {"key": "applicationName", "label": "应用名称", "type": "input", "placeholder": "请输入"}, {"key": "requestType", "label": "请求类型", "type": "select", "placeholder": "请选择", "options": [{"label": "GET", "value": "get"}, {"label": "POST", "value": "post"}, {"label": "PUT", "value": "put"}, {"label": "DELETE", "value": "delete"}]}, {"key": "discoveryTime", "label": "首次发现时间", "type": "select", "placeholder": "请选择", "options": [{"label": "今天", "value": "today"}, {"label": "昨天", "value": "yesterday"}, {"label": "最近7天", "value": "week"}, {"label": "最近30天", "value": "month"}]}], "showMoreSearchConditions": true, "quickSearch": {"placeholder": "请输入URL进行筛选"}, "toolbarActions": [{"key": "batch", "label": "批量操作", "type": "primary"}, {"key": "saveView", "label": "保存视图", "type": "default"}], "toolbarIcons": [{"key": "filter", "icon": "filter_alt", "tooltip": "筛选"}, {"key": "refresh", "icon": "refresh", "tooltip": "刷新"}, {"key": "columns", "icon": "view_column", "tooltip": "列设置"}], "groupTabs": [{"key": "high", "label": "高风险", "count": 34, "active": true}, {"key": "medium", "label": "中风险", "count": 38, "active": false}, {"key": "low", "label": "低风险", "count": 8, "active": false}, {"key": "none", "label": "无风险", "count": 124, "active": false}, {"key": "other", "label": "其他", "count": 8, "active": false}], "showCancelGroup": true, "rowActions": [{"key": "copy", "label": "📋"}, {"key": "more", "label": "⋯"}], "rowSelection": {"type": "checkbox"}, "pagination": {"current": 1, "pageSize": 25, "total": 34, "showSizeChanger": true, "showTotal": true}}, "events": {"onMount": {"type": "callApi", "config": {"viewDataApi": {"url": "/api/views", "method": "GET", "headers": {"Content-Type": "application/json"}, "timeout": 5000, "mockData": [{"key": "all", "title": "全部API", "count": 212, "searchConditions": {}}, {"key": "penetration", "title": "渗透测试重点API", "children": [{"key": "login", "title": "登录API", "count": 26, "searchConditions": {"apiTags": ["login"]}}, {"key": "url", "title": "URL重定向API", "count": 5, "searchConditions": {"apiTags": ["redirect"]}}, {"key": "response", "title": "单次响应数据量过大...", "count": 3, "searchConditions": {"responseSize": "large"}}, {"key": "sms", "title": "短信验证码发送API", "count": 2, "searchConditions": {"apiTags": ["sms"]}}, {"key": "register", "title": "注册API", "count": 0, "searchConditions": {"apiTags": ["register"]}}]}, {"key": "network", "title": "互联网敏感API", "children": [{"key": "3key", "title": "三要素", "count": 26, "searchConditions": {"sensitivityLevel": "high", "dataType": "personal"}}, {"key": "4key", "title": "四要素", "count": 5, "searchConditions": {"sensitivityLevel": "high", "dataType": "identity"}}]}]}, "tableDataApi": {"url": "/api/table/{viewKey}", "method": "GET", "headers": {"Content-Type": "application/json"}, "timeout": 5000, "mockData": {"all": [{"id": 1, "path": "/login", "sensitivity": "高敏感", "riskLevel": "高风险", "totalVisits": "3.6千", "trafficSource": "192.168.0.1", "firstSeen": "2025-08-14 19:18:21"}, {"id": 2, "path": "/abnormal", "sensitivity": "高敏感", "riskLevel": "高风险", "totalVisits": "2.5千", "trafficSource": "192.168.0.1", "firstSeen": "2025-08-14 19:19:12"}, {"id": 3, "path": "/api/users", "sensitivity": "中敏感", "riskLevel": "中风险", "totalVisits": "1.2千", "trafficSource": "192.168.0.2", "firstSeen": "2025-08-14 20:15:33"}, {"id": 4, "path": "/api/posts", "sensitivity": "低敏感", "riskLevel": "低风险", "totalVisits": "800", "trafficSource": "192.168.0.3", "firstSeen": "2025-08-14 21:22:45"}], "login": [{"id": 1, "path": "/login", "sensitivity": "高敏感", "riskLevel": "高风险", "totalVisits": "3.6千", "trafficSource": "192.168.0.1", "firstSeen": "2025-08-14 19:18:21"}]}}}}}}, {"id": "status_bar", "type": "StatusBar", "props": {"items": [{"key": "copyright", "label": "Copyright", "value": "© 2017-2025 All rights reserved."}, {"key": "version", "label": "版本", "value": "3.3.0.20250731"}]}}]}], "apis": [], "_metadata": {"createdAt": "2025-08-23T14:03:44.934Z", "updatedAt": "2025-08-24T12:39:43.624Z", "version": "1.0.0"}}