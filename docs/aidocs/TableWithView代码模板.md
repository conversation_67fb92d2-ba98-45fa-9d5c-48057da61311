# TableWithView组件代码实现模板

## 1. 组件接口定义

```typescript
// packages/shared/src/components/business/TableWithView.tsx

import React from 'react';
import { useMappedData } from '../../context/ComponentDataContext';
import { useEventHandler } from '../../hooks/useEventHandler';

// 继承原有接口定义
export interface TreeNode {
  key: string;
  title: string;
  icon?: string;
  href?: string;
  children?: TreeNode[];
  disabled?: boolean;
  count?: number;
}

export interface TableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number | string;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: any, index: number) => React.ReactNode;
}

export interface SearchField {
  key: string;
  label: string;
  type: 'input' | 'select' | 'date' | 'dateRange' | 'combinedSelect' | 'checkbox';
  placeholder?: string;
  options?: Array<{ label: string; value: any }>;
  combinedConfig?: {
    leftOptions?: Array<{ label: string; value: any }>;
    rightOptions?: Array<{ label: string; value: any }>;
  };
  checkboxConfig?: {
    text: string;
    tooltip?: string;
  };
}

export interface TableWithViewProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onSelect'> {
  // 基本配置
  title?: string;
  loading?: boolean;
  
  // 组件ID和事件配置
  componentId?: string;
  events?: Record<string, any>;
  
  // 左侧视图配置
  viewConfig?: {
    width?: number;
    searchPlaceholder?: string;
    showIcon?: boolean;
  };
  
  // 右侧表格配置
  tableConfig?: {
    columns: TableColumn[];
    searchFields?: SearchField[];
    toolbarActions?: Array<{
      key: string;
      label: string;
      type?: 'primary' | 'default' | 'danger';
    }>;
    quickSearch?: {
      placeholder?: string;
    };
    groupTabs?: Array<{
      key: string;
      label: string;
      count?: number;
      active?: boolean;
    }>;
    rowActions?: Array<{
      key: string;
      label: string;
      icon?: string;
    }>;
    pagination?: {
      current: number;
      pageSize: number;
      total: number;
      showSizeChanger?: boolean;
      showTotal?: boolean;
    };
    rowSelection?: {
      type: 'checkbox' | 'radio';
    };
  };
  
  // 样式配置
  style?: React.CSSProperties;
  className?: string;
  
  // 事件处理器
  onViewSelect?: (selectedKeys: string[], node: TreeNode) => void;
  onTableSearch?: (searchValues: Record<string, any>) => void;
  onTableChange?: (pagination: any, filters: any, sorter: any) => void;
  
  // 继承的事件处理器（不传递到DOM）
  onMount?: (data?: any) => void;
  onRowClick?: (data?: any) => void;
  onPageChange?: (data?: any) => void;
  onToolbarAction?: (data?: any) => void;
  onNodeClick?: (data?: any) => void;
  onNodeExpand?: (data?: any) => void;
  onMenuClick?: (data?: any) => void;
  onSecondaryMenuClick?: (data?: any) => void;
  onUserMenuClick?: (data?: any) => void;
  onActionClick?: (data?: any) => void;
}
```

## 2. 组件主体结构

```typescript
export const TableWithView: React.FC<TableWithViewProps> = ({
  title = '全部API',
  loading = false,
  componentId,
  events,
  viewConfig = {},
  tableConfig = {},
  style,
  className,
  onViewSelect,
  onTableSearch,
  onTableChange,
  ...rest
}) => {
  // 过滤掉事件处理器，避免传递到DOM元素
  const {
    onMount, onRowClick, onPageChange, onToolbarAction, onNodeClick,
    onNodeExpand, onMenuClick, onSecondaryMenuClick, onUserMenuClick,
    onActionClick, ...domProps
  } = rest;

  // 配置默认值
  const {
    width: viewWidth = 240,
    searchPlaceholder = '搜索视图',
    showIcon = true
  } = viewConfig;

  const {
    columns = [],
    searchFields = [],
    toolbarActions = [],
    quickSearch,
    groupTabs = [],
    rowActions = [],
    pagination,
    rowSelection
  } = tableConfig;

  // 状态管理
  const [selectedViewKeys, setSelectedViewKeys] = React.useState<string[]>(['all']);
  const [expandedKeys, setExpandedKeys] = React.useState<string[]>([]);
  const [viewSearchValue, setViewSearchValue] = React.useState<string>('');
  const [filteredViewData, setFilteredViewData] = React.useState<TreeNode[]>([]);
  
  const [searchValues, setSearchValues] = React.useState<Record<string, any>>({});
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<string[]>([]);
  const [sortConfig, setSortConfig] = React.useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);
  const [quickSearchValue, setQuickSearchValue] = React.useState('');

  // 数据源
  const viewData = useMappedData(componentId, 'onMount', true); // 树形数据
  const tableData = useMappedData(componentId, 'onMount', false); // 表格数据

  // 事件处理
  const eventHandler = useEventHandler(componentId, events);

  // 组件挂载时触发初始加载事件
  React.useEffect(() => {
    eventHandler.onMount({
      componentId,
      timestamp: Date.now()
    });
  }, [componentId, eventHandler]);

  // 处理视图数据变化
  React.useEffect(() => {
    if (viewData && viewData.length > 0) {
      setFilteredViewData(viewData);
      // 自动展开有子节点的节点
      const defaultExpanded = getDefaultExpandedKeys(viewData);
      setExpandedKeys(defaultExpanded);
    }
  }, [viewData]);

  // 视图搜索功能
  React.useEffect(() => {
    if (!viewSearchValue) {
      setFilteredViewData(viewData || []);
      return;
    }

    const filterTree = (nodes: TreeNode[]): TreeNode[] => {
      return nodes.reduce((acc: TreeNode[], node) => {
        const matchesSearch = node.title.toLowerCase().includes(viewSearchValue.toLowerCase());
        const filteredChildren = node.children ? filterTree(node.children) : [];

        if (matchesSearch || filteredChildren.length > 0) {
          acc.push({
            ...node,
            children: filteredChildren.length > 0 ? filteredChildren : node.children
          });
        }

        return acc;
      }, []);
    };

    const filtered = filterTree(viewData || []);
    setFilteredViewData(filtered);

    // 搜索时自动展开所有匹配的节点
    if (viewSearchValue) {
      const getAllKeys = (nodes: TreeNode[]): string[] => {
        let keys: string[] = [];
        nodes.forEach(node => {
          keys.push(node.key);
          if (node.children) {
            keys = keys.concat(getAllKeys(node.children));
          }
        });
        return keys;
      };
      setExpandedKeys(getAllKeys(filtered));
    }
  }, [viewSearchValue, viewData]);

  // 工具函数
  const getDefaultExpandedKeys = React.useCallback((data: TreeNode[]): string[] => {
    const keys: string[] = [];
    const traverse = (nodes: TreeNode[]) => {
      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          keys.push(node.key);
          traverse(node.children);
        }
      });
    };
    traverse(data);
    return keys;
  }, []);

  const getSearchConditionsByView = (node: TreeNode): Record<string, any> => {
    const conditions: Record<string, any> = {};
    
    // 根据视图节点生成对应的搜索条件
    switch (node.key) {
      case 'all':
        // 全部API - 无额外条件
        break;
      case 'login':
        conditions.apiTags = ['login'];
        break;
      case 'high_risk':
        conditions.riskLevel = 'high';
        break;
      case 'penetration':
        conditions.category = 'penetration';
        break;
      // 可以根据实际业务需求添加更多映射规则
      default:
        // 默认按节点key作为标签搜索
        if (node.key !== 'all') {
          conditions.tags = [node.key];
        }
        break;
    }
    
    return conditions;
  };

  // 事件处理函数
  const handleViewSelect = (selectedKeys: string[], node: TreeNode) => {
    if (node.disabled) return;

    setSelectedViewKeys(selectedKeys);
    
    // 根据选中的视图更新表格搜索条件
    const viewSearchConditions = getSearchConditionsByView(node);
    setSearchValues(prev => ({ ...prev, ...viewSearchConditions }));
    
    // 触发事件
    eventHandler.onNodeClick({
      node,
      selectedKeys,
      componentId,
      timestamp: Date.now()
    });
    
    onViewSelect?.(selectedKeys, node);

    // 如果有链接，进行跳转
    if (node.href) {
      window.location.href = node.href;
    }
  };

  const handleViewExpand = (node: TreeNode) => {
    const isExpanded = expandedKeys.includes(node.key);
    let newExpandedKeys: string[];

    if (isExpanded) {
      newExpandedKeys = expandedKeys.filter(key => key !== node.key);
    } else {
      newExpandedKeys = [...expandedKeys, node.key];
    }

    setExpandedKeys(newExpandedKeys);
    
    eventHandler.onNodeExpand({
      node,
      isExpanded: !isExpanded,
      expandedKeys: newExpandedKeys,
      componentId,
      timestamp: Date.now()
    });
  };

  const handleViewSearch = (value: string) => {
    setViewSearchValue(value);
    
    eventHandler.onSearch({
      searchValue: value,
      searchType: 'view',
      componentId,
      timestamp: Date.now()
    });
  };

  const handleTableSearch = (newSearchValues: Record<string, any>) => {
    setSearchValues(newSearchValues);
    
    eventHandler.onSearch({
      searchValues: newSearchValues,
      searchType: 'table',
      componentId,
      timestamp: Date.now()
    });
    
    onTableSearch?.(newSearchValues);
  };

  const handleTableReset = () => {
    setSearchValues({});
    onTableSearch?.({});
  };

  // 渲染函数将在下一部分定义...
```

## 3. 渲染函数模板

```typescript
  // 渲染左侧视图区域
  const renderViewSection = () => {
    return (
      <div style={{
        width: `${viewWidth}px`,
        height: '100%',
        backgroundColor: '#ffffff',
        borderRight: '1px solid #f0f0f0',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        boxShadow: '2px 0 8px rgba(0, 0, 0, 0.06)'
      }}>
        {/* 搜索框 */}
        <div style={{
          padding: '16px 12px 12px 12px',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <div style={{ position: 'relative' }}>
            <input
              type="text"
              placeholder={searchPlaceholder}
              value={viewSearchValue}
              onChange={(e) => handleViewSearch(e.target.value)}
              style={{
                width: '100%',
                height: '32px',
                padding: '8px 36px 8px 12px',
                border: '1px solid #e0e0e0',
                borderRadius: '16px',
                fontSize: '13px',
                outline: 'none',
                background: '#f8f9fa',
                transition: 'all 0.3s ease',
                color: '#333333',
                boxSizing: 'border-box'
              }}
            />
            <span style={{
              fontFamily: 'Material Icons Outlined',
              position: 'absolute',
              right: '10px',
              top: '50%',
              transform: 'translateY(-50%)',
              fontSize: '16px',
              color: '#999999',
              pointerEvents: 'none'
            }}>
              search
            </span>
          </div>
        </div>

        {/* 树形结构 */}
        <div style={{
          flex: 1,
          overflow: 'auto',
          paddingTop: '4px'
        }}>
          {filteredViewData.length > 0 ? (
            filteredViewData.map(node => renderTreeNode(node))
          ) : (
            <div style={{
              padding: '40px 20px',
              textAlign: 'center',
              color: '#cccccc',
              fontSize: '14px'
            }}>
              {viewSearchValue ? '没有找到匹配的结果' : '暂无数据'}
            </div>
          )}
        </div>
      </div>
    );
  };

  // 渲染右侧表格区域
  const renderTableSection = () => {
    return (
      <div style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: '#ffffff',
        padding: '12px'
      }}>
        {/* 这里复用TableViewWithSearch的渲染逻辑 */}
        {renderTopToolbar()}
        {renderSearchForm()}
        {renderGroupTabs()}
        {renderTable()}
        {renderPagination()}
      </div>
    );
  };

  // 主渲染函数
  const defaultStyle: React.CSSProperties = {
    height: '100%',
    backgroundColor: '#ffffff',
    borderRadius: 0,
    ...style,
  };

  return (
    <div className={`lowcode-table-with-view ${className || ''}`} style={defaultStyle} {...domProps}>
      <div style={{
        display: 'flex',
        height: '100%',
        backgroundColor: '#ffffff',
        borderRadius: '8px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        {renderViewSection()}
        {renderTableSection()}
      </div>
    </div>
  );
};
```

## 4. 组件导出和元数据

```typescript
// packages/shared/src/components/business/index.ts

export { TableWithView } from './TableWithView';
export type { TableWithViewProps, TreeNode } from './TableWithView';

// 组件元数据
export const tableWithViewMeta: ComponentMeta = {
  type: 'TableWithView',
  name: '表格视图组合',
  description: '左侧视图导航，右侧表格展示的组合组件',
  category: 'business',
  icon: 'table_view',
  props: [
    {
      name: 'title',
      type: 'string',
      description: '组件标题',
      default: '全部API'
    },
    {
      name: 'viewConfig',
      type: 'object',
      description: '左侧视图配置'
    },
    {
      name: 'tableConfig',
      type: 'object',
      description: '右侧表格配置'
    }
  ],
  events: [
    {
      name: 'onMount',
      description: '组件挂载时触发'
    },
    {
      name: 'onViewSelect',
      description: '视图选择时触发'
    },
    {
      name: 'onTableSearch',
      description: '表格搜索时触发'
    }
  ],
  defaultProps: {
    title: '全部API',
    viewConfig: {
      width: 240,
      searchPlaceholder: '搜索视图',
      showIcon: true
    }
  }
};
```

---

**说明**：此模板提供了TableWithView组件的基本代码结构，实际实现时需要：
1. 完整复制原组件的渲染函数（renderTreeNode、renderTopToolbar等）
2. 处理所有的样式细节
3. 完善错误处理和边界情况
4. 添加完整的TypeScript类型定义
