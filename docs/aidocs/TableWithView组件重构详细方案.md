# TableWithView组件重构详细方案

## 1. 项目背景

### 1.1 现有系统架构
- **TableViewWithSearch组件**：独立的表格搜索视图组件，包含搜索表单、工具栏、分组标签、表格和分页功能
- **SidebarTreeView组件**：独立的侧边栏树形导航组件，包含搜索框和树形结构
- **TopDownLayout布局**：当前为四段式布局（头部-侧边栏+内容-底部）

### 1.2 重构目标
1. **组件合并**：将TableViewWithSearch和SidebarTreeView合并为新的TableWithView组件
2. **布局调整**：TopDownLayout改为三段式布局（上-中-下）
3. **业务逻辑**：左侧视图保存搜索条件，点击视图时更新右侧表格数据
4. **保持兼容**：确保所有现有功能正常工作，保留原有样式

## 2. 现有组件分析

### 2.1 TableViewWithSearch组件特点
- **文件位置**：`packages/shared/src/components/business/TableViewWithSearch.tsx`
- **核心功能**：
  - 搜索表单（多种字段类型：input、select、date、combinedSelect、checkbox）
  - 工具栏（快速搜索、操作按钮、图标按钮）
  - 分组标签（高风险、中风险等）
  - 表格展示（排序、选择、行操作）
  - 分页控制
- **数据源**：通过useMappedData获取事件配置的数据
- **事件处理**：支持onMount、onSearch、onRowClick等事件

### 2.2 SidebarTreeView组件特点
- **文件位置**：`packages/shared/src/components/business/SidebarTreeView.tsx`
- **核心功能**：
  - 搜索框（固定配置：搜索视图）
  - 树形结构（支持展开/收起、选中状态、计数显示）
  - 节点点击处理
- **数据源**：通过useMappedData获取事件配置的数据
- **事件处理**：支持onMount、onNodeClick、onNodeExpand等事件

### 2.3 TopDownLayout当前结构
- **布局方式**：四段式（header + sidebar+content + footer）
- **组件识别**：通过data-component-type或data-component-id自动分配组件到对应插槽
- **样式配置**：固定的宽度、高度和间距设置

## 3. 重构设计方案

### 3.1 新组件TableWithView设计

#### 3.1.1 组件结构
```typescript
export interface TableWithViewProps extends React.HTMLAttributes<HTMLDivElement> {
  // 基本配置
  title?: string;
  loading?: boolean;
  
  // 组件ID和事件配置
  componentId?: string;
  events?: Record<string, any>;
  
  // 左侧视图配置（继承自SidebarTreeView）
  viewConfig?: {
    searchPlaceholder?: string;
    showIcon?: boolean;
  };
  
  // 右侧表格配置（继承自TableViewWithSearch）
  tableConfig?: {
    columns: TableColumn[];
    searchFields?: SearchField[];
    toolbarActions?: ToolbarAction[];
    quickSearch?: QuickSearchConfig;
    groupTabs?: GroupTab[];
    rowActions?: TableAction[];
    pagination?: PaginationConfig;
    rowSelection?: RowSelectionConfig;
  };
  
  // 样式配置
  style?: React.CSSProperties;
  className?: string;
  
  // 事件处理器
  onViewSelect?: (selectedKeys: string[], node: TreeNode) => void;
  onTableSearch?: (searchValues: Record<string, any>) => void;
  onTableChange?: (pagination: any, filters: any, sorter: any) => void;
}
```

#### 3.1.2 布局结构
```
┌─────────────────────────────────────────────────────────────┐
│                    TableWithView                            │
├─────────────────┬───────────────────────────────────────────┤
│                 │                                           │
│   左侧视图区域    │              右侧表格区域                  │
│                 │                                           │
│  ┌─────────────┐ │  ┌─────────────────────────────────────┐  │
│  │   搜索框    │ │  │            顶部工具栏               │  │
│  └─────────────┘ │  └─────────────────────────────────────┘  │
│  ┌─────────────┐ │  ┌─────────────────────────────────────┐  │
│  │             │ │  │            搜索表单                 │  │
│  │   树形结构   │ │  └─────────────────────────────────────┘  │
│  │             │ │  ┌─────────────────────────────────────┐  │
│  │             │ │  │            分组标签                 │  │
│  │             │ │  └─────────────────────────────────────┘  │
│  │             │ │  ┌─────────────────────────────────────┐  │
│  │             │ │  │                                     │  │
│  │             │ │  │            数据表格                 │  │
│  │             │ │  │                                     │  │
│  └─────────────┘ │  └─────────────────────────────────────┘  │
│                 │  ┌─────────────────────────────────────┐  │
│                 │  │            分页控制                 │  │
│                 │  └─────────────────────────────────────┘  │
└─────────────────┴───────────────────────────────────────────┘
```

### 3.2 TopDownLayout调整方案

#### 3.2.1 新布局接口
```typescript
export interface TopDownLayoutProps {
  header?: React.ReactNode;
  middle?: React.ReactNode;  // 新增中间区域
  footer?: React.ReactNode;
  children?: React.ReactNode;
  headerHeight?: number;
  footerHeight?: number;
  style?: React.CSSProperties;
  className?: string;
}
```

#### 3.2.2 布局结构调整
```
┌─────────────────────────────────────────────────────────────┐
│                        Header                               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                        Middle                               │
│                   (TableWithView)                           │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│                        Footer                               │
└─────────────────────────────────────────────────────────────┘
```

## 4. 实现步骤

### 4.1 第一阶段：创建TableWithView组件
1. **创建新组件文件**：`packages/shared/src/components/business/TableWithView.tsx`
2. **合并组件逻辑**：
   - 复制SidebarTreeView的搜索和树形结构代码到左侧区域
   - 复制TableViewWithSearch的表格相关代码到右侧区域
   - 统一事件处理和数据管理
3. **处理代码冲突**：
   - 合并相同的import和依赖
   - 统一状态管理（useState、useEffect）
   - 合并事件处理器（useEventHandler）
4. **样式调整**：
   - 设置左右布局（flex布局）
   - 保持原有组件的内部样式
   - 调整容器尺寸和间距

### 4.2 第二阶段：修改TopDownLayout
1. **更新布局接口**：添加middle属性，保留原有属性确保兼容性
2. **调整布局逻辑**：
   - 移除sidebar相关逻辑
   - 添加middle区域渲染
   - 更新组件识别逻辑
3. **更新样式定义**：三段式布局的CSS样式

### 4.3 第三阶段：更新Schema配置
1. **修改api-management.json**：
   - 移除独立的sidebar和api_table组件
   - 添加新的TableWithView组件到middle区域
   - 合并原有的事件配置和属性配置
2. **数据映射调整**：
   - 左侧视图使用树形数据映射
   - 右侧表格使用表格数据映射

### 4.4 第四阶段：业务逻辑实现
1. **视图选择逻辑**：
   - 左侧视图节点点击时保存搜索条件
   - 触发右侧表格数据更新
2. **数据联动**：
   - 实现视图与表格的数据关联
   - 处理搜索条件的传递和应用

## 5. 技术细节

### 5.1 代码合并策略
- **最小化重写**：尽可能复用现有代码，只调整布局和数据流
- **保持接口兼容**：新组件支持原有组件的所有配置项
- **渐进式迁移**：先实现基本功能，再优化细节

### 5.2 样式处理
- **保持原有样式**：左右两侧区域保持原组件的样式风格
- **响应式布局**：使用flex布局确保在不同屏幕尺寸下正常显示
- **边界处理**：添加适当的分割线和间距

### 5.3 事件处理
- **统一事件管理**：使用单一的useEventHandler实例
- **事件分发**：根据事件类型分发到对应的处理逻辑
- **数据同步**：确保左右两侧的数据状态同步

## 6. 风险评估

### 6.1 技术风险
- **代码冲突**：两个组件合并可能存在状态管理冲突
- **性能影响**：单个组件承载更多功能可能影响性能
- **兼容性问题**：布局调整可能影响现有页面

### 6.2 缓解措施
- **充分测试**：在开发环境充分测试所有功能
- **分步实施**：按阶段实施，每个阶段都进行验证
- **回滚准备**：保留原有组件，确保可以快速回滚

## 7. 验收标准

### 7.1 功能验收
- [ ] 左侧视图正常显示和交互
- [ ] 右侧表格正常显示和交互
- [ ] 视图选择能正确更新表格数据
- [ ] 所有原有功能正常工作

### 7.2 性能验收
- [ ] 组件加载时间不超过原有组件总和
- [ ] 交互响应时间保持在可接受范围
- [ ] 内存使用量不显著增加

### 7.3 兼容性验收
- [ ] 现有Schema配置能正常迁移
- [ ] 原有API接口保持兼容
- [ ] 样式在不同浏览器下正常显示

## 8. 后续优化

### 8.1 性能优化
- 实现虚拟滚动（如果数据量大）
- 优化重渲染逻辑
- 添加缓存机制

### 8.2 功能增强
- 支持视图的增删改
- 添加更多的数据联动方式
- 支持自定义布局比例

### 8.3 用户体验优化
- 添加加载状态指示
- 优化交互动画
- 支持键盘快捷键

## 9. 详细实现代码结构

### 9.1 TableWithView组件核心代码结构

```typescript
// packages/shared/src/components/business/TableWithView.tsx
export const TableWithView: React.FC<TableWithViewProps> = ({
  title = '全部API',
  loading = false,
  componentId,
  events,
  viewConfig = {},
  tableConfig = {},
  style,
  className,
  onViewSelect,
  onTableSearch,
  onTableChange,
  ...rest
}) => {
  // 状态管理
  const [selectedViewKeys, setSelectedViewKeys] = React.useState<string[]>(['all']);
  const [searchValues, setSearchValues] = React.useState<Record<string, any>>({});
  const [viewSearchValue, setViewSearchValue] = React.useState<string>('');

  // 数据源
  const viewData = useMappedData(componentId, 'onMount', true); // 树形数据
  const tableData = useMappedData(componentId, 'onMount', false); // 表格数据

  // 事件处理
  const eventHandler = useEventHandler(componentId, events);

  // 左侧视图选择处理
  const handleViewSelect = (selectedKeys: string[], node: TreeNode) => {
    setSelectedViewKeys(selectedKeys);

    // 根据选中的视图更新表格搜索条件
    const viewSearchConditions = getSearchConditionsByView(node);
    setSearchValues(viewSearchConditions);

    // 触发事件
    eventHandler.onNodeClick({ node, componentId, timestamp: Date.now() });
    onViewSelect?.(selectedKeys, node);
  };

  // 右侧表格搜索处理
  const handleTableSearch = (newSearchValues: Record<string, any>) => {
    setSearchValues(newSearchValues);
    eventHandler.onSearch({ searchValues: newSearchValues, componentId, timestamp: Date.now() });
    onTableSearch?.(newSearchValues);
  };

  return (
    <div className={`lowcode-table-with-view ${className || ''}`} style={defaultStyle} {...rest}>
      <div style={{ display: 'flex', height: '100%' }}>
        {/* 左侧视图区域 */}
        <div style={{ width: '240px', borderRight: '1px solid #f0f0f0' }}>
          {renderViewSection()}
        </div>

        {/* 右侧表格区域 */}
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          {renderTableSection()}
        </div>
      </div>
    </div>
  );
};
```

### 9.2 TopDownLayout修改代码

```typescript
// packages/shared/src/components/layout/TopDownLayout.tsx
export const TopDownLayout: React.FC<TopDownLayoutProps> = ({
  header,
  middle,  // 新增
  footer,
  children,
  headerHeight = 64,
  footerHeight = 32,
  style,
  className,
}) => {
  // 组件分配逻辑
  let headerComponent = header;
  let middleComponent = middle;
  let footerComponent = footer;

  if (React.Children.count(children) > 1) {
    const childrenArray = React.Children.toArray(children);

    childrenArray.forEach((child: any) => {
      if (React.isValidElement(child)) {
        const props = child.props as any;
        const componentType = props?.['data-component-type'];
        const componentId = props?.['data-component-id'];

        if (componentType === 'TopNavigation' || componentId === 'top_nav') {
          headerComponent = child;
        } else if (componentType === 'TableWithView' || componentId === 'table_with_view') {
          middleComponent = child;
        } else if (componentType === 'StatusBar' || componentId === 'status_bar') {
          footerComponent = child;
        }
      }
    });
  }

  return (
    <div className={`lowcode-topdown-layout ${className || ''}`} style={layoutStyle}>
      {/* 头部 */}
      {headerComponent && (
        <div style={headerStyle}>
          {headerComponent}
        </div>
      )}

      {/* 中间区域 */}
      {middleComponent && (
        <div style={middleStyle}>
          {middleComponent}
        </div>
      )}

      {/* 底部 */}
      {footerComponent && (
        <div style={footerStyle}>
          {footerComponent}
        </div>
      )}
    </div>
  );
};
```

### 9.3 Schema配置调整示例

```json
{
  "id": "api_management",
  "title": "全部API",
  "components": [
    {
      "id": "layout",
      "type": "TopDownLayout",
      "props": {
        "headerHeight": 64,
        "footerHeight": 32
      },
      "children": [
        {
          "id": "top_nav",
          "type": "TopNavigation",
          "props": { /* 原有配置 */ }
        },
        {
          "id": "table_with_view",
          "type": "TableWithView",
          "props": {
            "title": "全部API",
            "viewConfig": {
              "searchPlaceholder": "搜索视图",
              "showIcon": true
            },
            "tableConfig": {
              "columns": [ /* 原表格列配置 */ ],
              "searchFields": [ /* 原搜索字段配置 */ ],
              "toolbarActions": [ /* 原工具栏配置 */ ],
              "groupTabs": [ /* 原分组标签配置 */ ],
              "pagination": { /* 原分页配置 */ }
            }
          },
          "events": {
            "onMount": {
              "type": "callApi",
              "config": {
                "id": "onMount",
                "apiConfig": {
                  "url": "/api/combined/data",
                  "method": "GET",
                  "mockData": {
                    "viewData": [ /* 原sidebar数据 */ ],
                    "tableData": [ /* 原table数据 */ ]
                  },
                  "dataMapping": {
                    "viewData": {
                      "key": "key",
                      "title": "title",
                      "count": "count"
                    },
                    "tableData": {
                      "id": "id",
                      "path": "path",
                      "sensitivity": "sensitivity"
                    }
                  }
                }
              }
            }
          }
        },
        {
          "id": "status_bar",
          "type": "StatusBar",
          "props": { /* 原有配置 */ }
        }
      ]
    }
  ]
}
```

## 10. 数据流设计

### 10.1 数据获取流程
```
1. TableWithView组件挂载
2. 触发onMount事件
3. API调用返回合并数据 { viewData: [], tableData: [] }
4. useMappedData分别处理两种数据类型
5. 左侧渲染viewData，右侧渲染tableData
```

### 10.2 交互数据流
```
1. 用户点击左侧视图节点
2. handleViewSelect被调用
3. 根据节点信息生成搜索条件
4. 更新searchValues状态
5. 触发表格数据重新筛选
6. 右侧表格更新显示
```

### 10.3 搜索条件映射规则
```typescript
const getSearchConditionsByView = (node: TreeNode): Record<string, any> => {
  const conditions: Record<string, any> = {};

  switch (node.key) {
    case 'all':
      // 全部API - 无额外条件
      break;
    case 'login':
      conditions.apiTags = ['login'];
      break;
    case 'high_risk':
      conditions.riskLevel = 'high';
      break;
    // ... 其他视图条件映射
  }

  return conditions;
};
```

## 11. 测试计划

### 11.1 单元测试
- [ ] TableWithView组件渲染测试
- [ ] 左侧视图交互测试
- [ ] 右侧表格功能测试
- [ ] 数据联动逻辑测试
- [ ] TopDownLayout布局测试

### 11.2 集成测试
- [ ] 完整页面渲染测试
- [ ] 事件系统集成测试
- [ ] API数据获取测试
- [ ] Schema配置解析测试

### 11.3 用户体验测试
- [ ] 交互响应速度测试
- [ ] 视觉效果验证
- [ ] 不同屏幕尺寸适配测试
- [ ] 浏览器兼容性测试

## 12. 部署和迁移

### 12.1 迁移步骤
1. **开发环境验证**：在开发环境完成所有功能开发和测试
2. **Schema迁移工具**：编写自动化脚本转换现有Schema配置
3. **灰度发布**：先在部分页面使用新组件
4. **全量迁移**：确认稳定后全面替换旧组件
5. **清理工作**：移除不再使用的旧组件代码

### 12.2 回滚方案
- 保留原有组件代码作为备份
- 准备快速回滚的Schema配置
- 监控系统性能和错误率
- 建立快速响应机制

---

**文档版本**：v1.0
**创建时间**：2025-08-25
**最后更新**：2025-08-25
